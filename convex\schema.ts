import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";
import { authTables } from "@convex-dev/auth/server";

export default defineSchema({
  // Include Convex Auth tables
  ...authTables,
  
  // Users table - stores user information and profiles
  users: defineTable({
    email: v.string(),
    username: v.string(),
    displayName: v.string(),
    avatar: v.optional(v.string()),
    bio: v.optional(v.string()),
    joinDate: v.number(),
    isOnline: v.boolean(),
    lastActive: v.number(),
    // Gaming statistics
    gamesPlayed: v.number(),
    gamesWon: v.number(),
    totalPlaytime: v.number(),
    // User preferences
    preferences: v.object({
      notifications: v.boolean(),
      soundEnabled: v.boolean(),
      autoJoinGames: v.boolean(),
      theme: v.string(),
    }),
    // Virtual currency
    credits: v.number(),
  })
    .index("by_email", ["email"])
    .index("by_username", ["username"])
    .index("by_online_status", ["isOnline"]),

  // Games table - stores available games in the catalog
  games: defineTable({
    title: v.string(),
    description: v.string(),
    shortDescription: v.string(),
    genre: v.array(v.string()),
    images: v.object({
      header: v.string(),
      screenshots: v.array(v.string()),
      thumbnail: v.string(),
    }),
    price: v.number(),
    rating: v.number(),
    reviewCount: v.number(),
    developer: v.string(),
    publisher: v.string(),
    releaseDate: v.number(),
    // Game type specific data
    gameType: v.union(
      v.literal("poker"),
      v.literal("blackjack"),
      v.literal("uno"),
      v.literal("gofish")
    ),
    maxPlayers: v.number(),
    minPlayers: v.number(),
    estimatedPlayTime: v.number(),
    // Game rules and configuration
    rules: v.object({
      description: v.string(),
      objective: v.string(),
      setup: v.string(),
    }),
    isActive: v.boolean(),
    featured: v.boolean(),
  })
    .index("by_genre", ["genre"])
    .index("by_game_type", ["gameType"])
    .index("by_rating", ["rating"])
    .index("by_price", ["price"])
    .index("by_featured", ["featured"]),

  // User Library - tracks which games users own
  userLibrary: defineTable({
    userId: v.id("users"),
    gameId: v.id("games"),
    purchaseDate: v.number(),
    playtime: v.number(),
    lastPlayed: v.optional(v.number()),
    achievements: v.array(v.string()),
    personalRating: v.optional(v.number()),
    hasReviewed: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_game", ["gameId"])
    .index("by_user_game", ["userId", "gameId"])
    .index("by_last_played", ["userId", "lastPlayed"]),

  // Game Sessions - manages multiplayer game instances
  gameSessions: defineTable({
    gameId: v.id("games"),
    hostId: v.id("users"),
    players: v.array(v.object({
      userId: v.id("users"),
      username: v.string(),
      avatar: v.optional(v.string()),
      joinedAt: v.number(),
      isReady: v.boolean(),
      position: v.number(),
    })),
    spectators: v.array(v.object({
      userId: v.id("users"),
      username: v.string(),
      joinedAt: v.number(),
    })),
    status: v.union(
      v.literal("waiting"),
      v.literal("starting"),
      v.literal("active"),
      v.literal("paused"),
      v.literal("finished"),
      v.literal("cancelled")
    ),
    currentTurn: v.optional(v.number()),
    turnStartTime: v.optional(v.number()),
    turnTimeLimit: v.number(),
    // Game state - stored as JSON string for flexibility
    gameState: v.optional(v.string()),
    startTime: v.optional(v.number()),
    endTime: v.optional(v.number()),
    winner: v.optional(v.id("users")),
    isPrivate: v.boolean(),
    password: v.optional(v.string()),
    maxPlayers: v.number(),
    settings: v.object({
      allowSpectators: v.boolean(),
      autoStart: v.boolean(),
      difficulty: v.optional(v.string()),
    }),
  })
    .index("by_game", ["gameId"])
    .index("by_host", ["hostId"])
    .index("by_status", ["status"])
    .index("by_game_status", ["gameId", "status"]),

  // Friends system
  friends: defineTable({
    userId: v.id("users"),
    friendId: v.id("users"),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("blocked")
    ),
    requestDate: v.number(),
    acceptDate: v.optional(v.number()),
    requestedBy: v.id("users"),
  })
    .index("by_user", ["userId"])
    .index("by_friend", ["friendId"])
    .index("by_user_friend", ["userId", "friendId"])
    .index("by_status", ["status"]),

  // Chat messages
  chatMessages: defineTable({
    senderId: v.id("users"),
    senderUsername: v.string(),
    recipientId: v.optional(v.id("users")), // null for global chat
    gameSessionId: v.optional(v.id("gameSessions")), // for in-game chat
    message: v.string(),
    timestamp: v.number(),
    type: v.union(
      v.literal("global"),
      v.literal("private"),
      v.literal("game"),
      v.literal("system")
    ),
    edited: v.boolean(),
    editedAt: v.optional(v.number()),
  })
    .index("by_sender", ["senderId"])
    .index("by_recipient", ["recipientId"])
    .index("by_game_session", ["gameSessionId"])
    .index("by_timestamp", ["timestamp"])
    .index("by_type", ["type"]),

  // Game reviews and ratings
  gameReviews: defineTable({
    userId: v.id("users"),
    gameId: v.id("games"),
    username: v.string(),
    rating: v.number(), // 1-5 stars
    review: v.string(),
    helpful: v.number(), // helpful votes
    timestamp: v.number(),
    playtime: v.number(), // playtime when review was written
    recommended: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_game", ["gameId"])
    .index("by_rating", ["rating"])
    .index("by_helpful", ["helpful"])
    .index("by_timestamp", ["timestamp"]),

  // Achievements system
  achievements: defineTable({
    gameId: v.optional(v.id("games")), // null for global achievements
    name: v.string(),
    description: v.string(),
    icon: v.string(),
    type: v.union(
      v.literal("progression"),
      v.literal("skill"),
      v.literal("social"),
      v.literal("hidden")
    ),
    requirements: v.object({
      description: v.string(),
      target: v.number(),
      metric: v.string(), // e.g., "games_won", "friends_added"
    }),
    rarity: v.union(
      v.literal("common"),
      v.literal("uncommon"),
      v.literal("rare"),
      v.literal("legendary")
    ),
    points: v.number(),
    isActive: v.boolean(),
  })
    .index("by_game", ["gameId"])
    .index("by_type", ["type"])
    .index("by_rarity", ["rarity"]),

  // User achievements
  userAchievements: defineTable({
    userId: v.id("users"),
    achievementId: v.id("achievements"),
    unlockedAt: v.number(),
    progress: v.number(),
    isCompleted: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_achievement", ["achievementId"])
    .index("by_user_achievement", ["userId", "achievementId"])
    .index("by_completed", ["isCompleted"]),

  // Wishlists
  wishlists: defineTable({
    userId: v.id("users"),
    gameId: v.id("games"),
    addedAt: v.number(),
    notifyOnSale: v.boolean(),
  })
    .index("by_user", ["userId"])
    .index("by_game", ["gameId"])
    .index("by_user_game", ["userId", "gameId"]),

  // Game invitations
  gameInvitations: defineTable({
    fromUserId: v.id("users"),
    toUserId: v.id("users"),
    gameSessionId: v.id("gameSessions"),
    message: v.optional(v.string()),
    status: v.union(
      v.literal("pending"),
      v.literal("accepted"),
      v.literal("declined"),
      v.literal("expired")
    ),
    sentAt: v.number(),
    respondedAt: v.optional(v.number()),
    expiresAt: v.number(),
  })
    .index("by_from_user", ["fromUserId"])
    .index("by_to_user", ["toUserId"])
    .index("by_session", ["gameSessionId"])
    .index("by_status", ["status"]),

  // Notifications
  notifications: defineTable({
    userId: v.id("users"),
    type: v.union(
      v.literal("friend_request"),
      v.literal("game_invitation"),
      v.literal("achievement_unlocked"),
      v.literal("game_finished"),
      v.literal("system")
    ),
    title: v.string(),
    message: v.string(),
    data: v.optional(v.string()), // JSON string for additional data
    isRead: v.boolean(),
    timestamp: v.number(),
    expiresAt: v.optional(v.number()),
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_read", ["isRead"])
    .index("by_timestamp", ["timestamp"]),
});