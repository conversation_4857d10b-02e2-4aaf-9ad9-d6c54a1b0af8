"use client";

import React, { createContext, useContext, useEffect, useState } from "react";

interface User {
  _id: string;
  email: string;
  username: string;
  displayName: string;
  avatar?: string;
  bio?: string;
  isOnline: boolean;
  credits: number;
  gamesPlayed: number;
  gamesWon: number;
  totalPlaytime: number;
}

interface AppContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
}

const AppContext = createContext<AppContextType | undefined>(undefined);

export function AppProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // Mock authentication - in real app this would integrate with your auth system
  useEffect(() => {
    // Simulate checking for existing session
    const mockUser: User = {
      _id: "current-user-id",
      email: "<EMAIL>",
      username: "DemoPlayer",
      displayName: "Demo Player",
      avatar: "/api/placeholder/128/128",
      bio: "Welcome to GameHub! This is a demo account.",
      isOnline: true,
      credits: 1500,
      gamesPlayed: 42,
      gamesWon: 28,
      totalPlaytime: 1680, // minutes
    };

    // Simulate loading delay
    setTimeout(() => {
      setUser(mockUser);
      setIsAuthenticated(true);
      setIsLoading(false);
    }, 1000);
  }, []);

  const login = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      // Mock login - in real app this would call your auth API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const mockUser: User = {
        _id: "user-" + Date.now(),
        email,
        username: email.split("@")[0] || "user",
        displayName: email.split("@")[0] || "User",
        isOnline: true,
        credits: 1000,
        gamesPlayed: 0,
        gamesWon: 0,
        totalPlaytime: 0,
      };

      setUser(mockUser);
      setIsAuthenticated(true);
    } catch (error) {
      console.error("Login failed:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
  };

  const updateUser = (updates: Partial<User>) => {
    if (user) {
      setUser({ ...user, ...updates });
    }
  };

  return (
    <AppContext.Provider
      value={{
        user,
        isAuthenticated,
        isLoading,
        login,
        logout,
        updateUser,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (context === undefined) {
    throw new Error("useApp must be used within an AppProvider");
  }
  return context;
}

// Hook for getting current user data
export function useCurrentUser() {
  const { user, isAuthenticated, isLoading } = useApp();
  return { user, isAuthenticated, isLoading };
}

// Hook for auth actions
export function useAuth() {
  const { login, logout, isLoading } = useApp();
  return { login, logout, isLoading };
}