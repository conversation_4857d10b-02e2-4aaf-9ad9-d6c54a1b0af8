import { convexAuth } from "@convex-dev/auth/server";
import { Password } from "@convex-dev/auth/providers/Password";
import { query } from "./_generated/server";
import type { DataModel } from "./_generated/dataModel";

const CustomPassword = Password<DataModel>({
  profile(params) {
    const email = params.email as string;
    const name = params.name as string;
    const username = email.split("@")[0] || "user";

    return {
      email,
      username,
      displayName: name || username,
      joinDate: Date.now(),
      isOnline: true,
      lastActive: Date.now(),
      gamesPlayed: 0,
      gamesWon: 0,
      totalPlaytime: 0,
      preferences: {
        notifications: true,
        soundEnabled: true,
        autoJoinGames: false,
        theme: "dark",
      },
      credits: 1000, // Starting credits
    };
  },
});

export const { auth, signIn, signOut, store } = convexAuth({
  providers: [CustomPassword],
});

// Query to get current user
export const currentUser = query({
  args: {},
  handler: async (ctx) => {
    const userId = await auth.getUserId(ctx);
    if (!userId) {
      return null;
    }

    // With Convex Auth, the user data from the profile function is automatically stored
    // in the auth tables. We can get it directly using the userId.
    const user = await ctx.db.get(userId);
    return user;
  },
});