const fs = require('fs');
const { execSync } = require('child_process');

// Read the private key
const privateKey = fs.readFileSync('private_key.pem', 'utf8');
const publicKey = fs.readFileSync('public_key.pem', 'utf8');

console.log('Setting JWT_PRIVATE_KEY...');
try {
  // Use execSync to set the environment variable
  execSync(`npx convex env set JWT_PRIVATE_KEY "${privateKey}"`, { 
    stdio: 'inherit',
    shell: true 
  });
  console.log('✅ JWT_PRIVATE_KEY set successfully');
} catch (error) {
  console.error('❌ Failed to set JWT_PRIVATE_KEY:', error.message);
}

console.log('\n📋 Public key for Convex configuration:');
console.log(publicKey);
console.log('\n💡 You may need to configure this public key in your Convex dashboard for JWT verification.');
